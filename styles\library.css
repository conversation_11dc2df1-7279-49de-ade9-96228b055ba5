/* Library Page Styles */
.library-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  color: #fff;
}

.library-header {
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-in-out;
}

.library-header h1 {
  font-size: 42px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #fff;
}

.library-header p {
  font-size: 16px;
  color: #b3b3b3;
}

.library-content {
  width: 100%;
  animation: slideUp 0.8s ease-in-out;
}

.empty-library {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
}

.empty-library i {
  font-size: 80px;
  color: #36e2ec;
  margin-bottom: 20px;
}

.empty-library h2 {
  font-size: 24px;
  margin-bottom: 15px;
}

.empty-library p {
  font-size: 16px;
  color: #b3b3b3;
  max-width: 500px;
  margin-bottom: 25px;
}

.empty-library .action-btn {
  background-color: rgba(105, 105, 170, 0.7);
  border: none;
  color: #fff;
  padding: 12px 25px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  text-decoration: none;
}

.empty-library .action-btn:hover {
  background-color: #36e2ec;
  transform: scale(1.05);
}

.empty-library .action-btn i {
  font-size: 18px;
  margin-right: 8px;
}

.library-songs {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.library-song-item {
  background-color: rgba(105, 105, 170, 0.1);
  border-radius: 10px;
  padding: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.library-song-item:hover {
  background-color: rgba(105, 105, 170, 0.3);
  transform: translateY(-5px);
}

.library-song-item img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 5px;
  margin-bottom: 10px;
}

.library-song-item h5 {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 5px;
}

.library-song-item .subtitle {
  font-size: 12px;
  color: #b3b3b3;
}

.library-song-item .song-actions {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.library-song-item:hover .song-actions {
  opacity: 1;
}

.library-song-item .song-actions i {
  font-size: 20px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.library-song-item .song-actions i:hover {
  color: #36e2ec;
  transform: scale(1.1);
}

.library-song-item .img_play {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
}

.library-song-item .img_play img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-bottom: 0;
}

.library-song-item .img_play i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40px;
  color: #36e2ec;
  opacity: 0;
  transition: opacity 0.3s ease;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.5));
}

.library-song-item .img_play:hover i {
  opacity: 1;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .library-songs {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .library-songs {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 576px) {
  .library-header h1 {
    font-size: 32px;
  }
  
  .library-songs {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
}
