/* Common CSS styles for consistent styling across all pages */

/* Common variables */
:root {
  --primary-color: #36e2ec;
  --secondary-color: rgba(105, 105, 170, 0.7);
  --background-color: #131312;
  --text-color: #fff;
  --subtitle-color: #b3b3b3;
  --hover-color: rgba(105, 105, 170, 0.3);
  --active-color: rgba(105, 105, 170, 0.7);
  --border-radius: 10px;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  --transition: all 0.3s ease;
}

/* Common animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Common button styles */
.action-btn {
  background-color: var(--secondary-color);
  border: none;
  color: var(--text-color);
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.action-btn:hover {
  background-color: var(--primary-color);
  transform: scale(1.05);
}

.action-btn.in-library {
  background-color: rgba(54, 226, 236, 0.7);
}

.action-btn i {
  margin-right: 8px;
  font-size: 16px;
}

/* Common song item styles */
.songItem {
  position: relative;
  transition: var(--transition);
}

.songItem:hover {
  background-color: var(--hover-color) !important;
}

.songItem h5 {
  color: var(--text-color);
}

.songItem .subtitle {
  color: var(--subtitle-color);
  font-size: smaller;
}

/* Common song actions */
.song-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.songItem:hover .song-actions {
  opacity: 1;
}

.add-to-library {
  cursor: pointer;
  color: var(--text-color);
  font-size: 18px;
  transition: var(--transition);
}

.add-to-library:hover {
  color: var(--primary-color);
  transform: scale(1.1);
}

/* Fix for menu_side and song_side alignment */
.menu_side, .song_side {
  height: 100%;
  overflow-y: auto;
}

/* Fix for master_play positioning */
.master_play {
  position: fixed;
  bottom: 0;
  width: 85%;
  z-index: 999;
}

/* Fix for search results */
.search_result {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
  z-index: 1000;
}

/* Fix for responsive layouts */
@media (max-width: 992px) {
  header {
    width: 95%;
  }
  
  .master_play {
    width: 95%;
  }
}

@media (max-width: 768px) {
  .menu_side {
    width: 100%;
    z-index: 999;
  }
  
  .song_side {
    width: 100%;
    z-index: 1;
  }
  
  .master_play {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .menu_side h1 {
    font-size: 20px;
  }
  
  .master_play h5 {
    font-size: 12px;
  }
}
