// Recently Played functionality for music app

document.addEventListener("DOMContentLoaded", () => {
  // Initialize recently played functionality
  initializeRecentlyPlayed();
  initializeRecentlyPlayedSearch();
});

// Initialize recently played functionality
function initializeRecentlyPlayed() {
  // Get recently played elements
  const emptyRecentlyPlayed = document.getElementById("empty-recently-played");
  const recentlyPlayedSongs = document.getElementById("recently-played-songs");

  // If recently played elements don't exist, return
  if (!emptyRecentlyPlayed || !recentlyPlayedSongs) return;

  // Get recently played songs from localStorage
  const recentlyPlayedData = getRecentlyPlayedSongs();

  // Check if recently played is empty
  if (recentlyPlayedData.length === 0) {
    // Show empty recently played message
    emptyRecentlyPlayed.style.display = "flex";
    recentlyPlayedSongs.style.display = "none";
  } else {
    // Hide empty recently played message and show songs
    emptyRecentlyPlayed.style.display = "none";
    recentlyPlayedSongs.style.display = "grid";

    // Populate recently played songs
    populateRecentlyPlayedSongs(recentlyPlayedData);
  }

  // Add clear history button if there are songs
  if (recentlyPlayedData.length > 0) {
    addClearHistoryButton();
  }
}

// Get recently played songs from localStorage
function getRecentlyPlayedSongs() {
  // Get recently played songs from localStorage
  const recentlyPlayedSongs = localStorage.getItem("recentlyPlayedSongs");

  // If recently played songs exist, parse and return them
  if (recentlyPlayedSongs) {
    return JSON.parse(recentlyPlayedSongs);
  }

  // Otherwise, return empty array
  return [];
}

// Save recently played songs to localStorage
function saveRecentlyPlayedSongs(songs) {
  // Save recently played songs to localStorage
  localStorage.setItem("recentlyPlayedSongs", JSON.stringify(songs));
}

// Add song to recently played history
function addSongToRecentlyPlayed(songId) {
  // Get recently played songs
  let recentlyPlayedSongs = getRecentlyPlayedSongs();

  // Create new entry with timestamp
  const newEntry = {
    songId: songId,
    timestamp: new Date().toISOString(),
    playedAt: Date.now(),
  };

  // Remove existing entry if it exists (to avoid duplicates)
  recentlyPlayedSongs = recentlyPlayedSongs.filter(
    (entry) => entry.songId !== songId
  );

  // Add new entry to the beginning
  recentlyPlayedSongs.unshift(newEntry);

  // Limit to last 50 songs to prevent unlimited storage growth
  if (recentlyPlayedSongs.length > 50) {
    recentlyPlayedSongs = recentlyPlayedSongs.slice(0, 50);
  }

  // Save updated recently played songs
  saveRecentlyPlayedSongs(recentlyPlayedSongs);

  // If on recently played page, refresh the display
  if (window.location.pathname.includes("recently-played.html")) {
    initializeRecentlyPlayed();
  }

  return true; // Song added successfully
}

// Clear recently played history
function clearRecentlyPlayedHistory() {
  // Clear recently played songs from localStorage
  localStorage.removeItem("recentlyPlayedSongs");

  // If on recently played page, refresh the display
  if (window.location.pathname.includes("recently-played.html")) {
    initializeRecentlyPlayed();
  }

  // Show notification
  if (typeof showNotification === "function") {
    showNotification("Đã xóa lịch sử phát nhạc", 3000);
  }
}

// Populate recently played songs
function populateRecentlyPlayedSongs(recentlyPlayedEntries) {
  // Get recently played songs container
  const recentlyPlayedContainer = document.getElementById(
    "recently-played-songs"
  );

  // Clear existing songs
  recentlyPlayedContainer.innerHTML = "";

  // Get song data for recently played entries
  const recentlyPlayedTracks = recentlyPlayedEntries
    .map((entry) => {
      const song = songs.find((song) => song.id === entry.songId);
      return song
        ? { ...song, playedAt: entry.playedAt, timestamp: entry.timestamp }
        : null;
    })
    .filter((song) => song !== null);

  // Add songs to container
  recentlyPlayedTracks.forEach((song) => {
    const songElement = document.createElement("div");
    songElement.className = "recently-played-song-item";

    // Format play time
    const playTime = formatPlayTime(song.timestamp);

    songElement.innerHTML = `
      <div class="img_play">
        <img src="${song.poster}" alt="Song Cover">
        <i class="bi playListPlay bi-play-circle-fill" id="${song.id}"></i>
      </div>
      <h5>${extractSongName(song.songName)}</h5>
      <div class="subtitle">${extractArtistName(song.songName)}</div>
      <div class="play-time">${playTime}</div>
      <div class="song-actions">
        <i class="bi ${
          isSongInLibrary(song.id) ? "bi-heart-fill in-library" : "bi-heart"
        } add-to-library"
           data-id="${song.id}" title="${
      isSongInLibrary(song.id) ? "Remove from Library" : "Add to Library"
    }"></i>
      </div>
    `;

    // Add click event to navigate to the song detail page
    songElement.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button or action buttons
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("add-to-library")
      ) {
        window.location.href = `music-detail.html?id=${song.id}`;
      }
    });

    recentlyPlayedContainer.appendChild(songElement);
  });

  // Add event listeners to play buttons
  Array.from(
    document.querySelectorAll(".recently-played-song-item .playListPlay")
  ).forEach((element) => {
    element.addEventListener("click", (e) => {
      const songId = e.target.id;
      playSong(songId);
    });
  });

  // Add event listeners to library buttons
  Array.from(
    document.querySelectorAll(".recently-played-song-item .add-to-library")
  ).forEach((element) => {
    element.addEventListener("click", (e) => {
      e.stopPropagation();
      const songId = e.target.getAttribute("data-id");

      if (isSongInLibrary(songId)) {
        removeSongFromLibrary(songId);
        e.target.classList.remove("bi-heart-fill", "in-library");
        e.target.classList.add("bi-heart");
        e.target.title = "Add to Library";
      } else {
        addSongToLibrary(songId);
        e.target.classList.remove("bi-heart");
        e.target.classList.add("bi-heart-fill", "in-library");
        e.target.title = "Remove from Library";
      }
    });
  });
}

// Format play time for display
function formatPlayTime(timestamp) {
  const now = new Date();
  const playTime = new Date(timestamp);
  const diffInMinutes = Math.floor((now - playTime) / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 1) {
    return "Vừa xong";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} phút trước`;
  } else if (diffInHours < 24) {
    return `${diffInHours} giờ trước`;
  } else if (diffInDays < 7) {
    return `${diffInDays} ngày trước`;
  } else {
    return playTime.toLocaleDateString("vi-VN");
  }
}

// Add clear history button
function addClearHistoryButton() {
  const header = document.querySelector(".recently-played-header");
  if (!header || header.querySelector(".clear-history-btn")) return;

  const headerControls = document.createElement("div");
  headerControls.className = "recently-played-header-controls";

  const headerContent = header.innerHTML;
  header.innerHTML = "";

  const headerInfo = document.createElement("div");
  headerInfo.innerHTML = headerContent;

  const clearButton = document.createElement("button");
  clearButton.className = "clear-history-btn";
  clearButton.innerHTML = '<i class="bi bi-trash"></i> Xóa lịch sử';
  clearButton.addEventListener("click", () => {
    if (confirm("Bạn có chắc chắn muốn xóa toàn bộ lịch sử phát nhạc?")) {
      clearRecentlyPlayedHistory();
    }
  });

  headerControls.appendChild(headerInfo);
  headerControls.appendChild(clearButton);
  header.appendChild(headerControls);
}

// Initialize search functionality for recently played
function initializeRecentlyPlayedSearch() {
  // Get search input
  const searchInput = document.querySelector(".search input");

  // If search input doesn't exist, return
  if (!searchInput) return;

  // Add search input event listener
  searchInput.addEventListener("input", () => {
    const searchValue = searchInput.value.toLowerCase();

    // Get all recently played song items
    const songItems = document.querySelectorAll(".recently-played-song-item");

    // Filter songs based on search value
    songItems.forEach((item) => {
      const songTitle = item.querySelector("h5").textContent.toLowerCase();
      const artistName = item
        .querySelector(".subtitle")
        .textContent.toLowerCase();

      if (songTitle.includes(searchValue) || artistName.includes(searchValue)) {
        item.style.display = "block";
      } else {
        item.style.display = "none";
      }
    });
  });
}

// Play a song from recently played
function playSong(songId) {
  // Set the current song index
  index = parseInt(songId);

  // Update the music player
  music.src = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;

  // Find the song in the songs array
  const song = songs.find((song) => song.id === songId);

  // Update the title
  title.innerHTML = song.songName;

  // Update download link
  download_music.href = `./audio/${index}.mp3`;
  download_music.setAttribute("download", extractSongName(song.songName));

  // Play the song
  music.play();

  // Update UI
  masterPlay.classList.remove("bi-play-fill");
  masterPlay.classList.add("bi-pause-fill");

  // Add wave animation
  wave.classList.add("active2");

  // Make all play buttons to play icon
  makeAllPlays();

  // Update the current playing song UI
  document.getElementById(`${songId}`).classList.remove("bi-play-circle-fill");
  document.getElementById(`${songId}`).classList.add("bi-pause-circle-fill");

  // Add to recently played history
  addSongToRecentlyPlayed(songId);
}

// Extract song name from HTML string
function extractSongName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the text content (song name) without the subtitle
  const songName = tempDiv.childNodes[0].textContent.trim();
  return songName;
}

// Extract artist name from HTML string
function extractArtistName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the subtitle div content (artist name)
  const artistElement = tempDiv.querySelector(".subtitle");
  return artistElement ? artistElement.textContent.trim() : "Unknown Artist";
}
