/* Notification Component Styles */
.notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: rgba(54, 226, 236, 0.9);
  color: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  z-index: 9999;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 300px;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

.notification i {
  font-size: 20px;
  margin-right: 10px;
}

.notification-message {
  font-size: 14px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 576px) {
  .notification {
    bottom: 20px;
    right: 20px;
    left: 20px;
    max-width: none;
  }
}
